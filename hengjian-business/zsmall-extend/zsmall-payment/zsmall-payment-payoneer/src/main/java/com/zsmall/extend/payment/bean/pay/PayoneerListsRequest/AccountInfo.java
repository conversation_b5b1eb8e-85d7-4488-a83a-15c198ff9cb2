package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class AccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String method;
    private String timestamp;
    private String creationDate;
    private String updateDate;
    private String passwordChangeDate;
}
