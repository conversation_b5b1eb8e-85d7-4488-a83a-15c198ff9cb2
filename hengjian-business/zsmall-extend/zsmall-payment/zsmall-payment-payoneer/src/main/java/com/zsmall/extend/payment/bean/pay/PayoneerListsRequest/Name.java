package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class Name implements Serializable {

    private static final long serialVersionUID = 1L;

    private String title;
    private String firstName;
    private String middleName;
    private String lastName;
    private String maidenName;
}
