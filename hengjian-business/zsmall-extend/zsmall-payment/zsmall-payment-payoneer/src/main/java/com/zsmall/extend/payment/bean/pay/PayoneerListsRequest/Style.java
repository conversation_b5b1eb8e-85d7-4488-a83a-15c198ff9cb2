package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class Style implements Serializable {

    private static final long serialVersionUID = 1L;

    private String language;
    private String cssOverride;
    private String resolution;
    private String hostedVersion;
    private String challengeWindowSize;
    private String displayName;
    private String primaryColor;
    private String logoUrl;
    private String backgroundType;
    private String backgroundColor;
    private String backgroundImageUrl;
}
