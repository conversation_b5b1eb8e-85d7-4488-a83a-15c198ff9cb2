package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.OffsetDateTime;

@Data
@Accessors(chain = true)
public class Account implements Serializable {

    private static final long serialVersionUID = 1L;

    private OffsetDateTime registrationDate;
    private Boolean changedDuringCheckout;
}
