package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class Callback implements Serializable {

    private static final long serialVersionUID = 1L;

    private String returnUrl;
    private String summaryUrl;
    private String cancelUrl;
    private String backToShopUrl;
    private String notificationUrl;
    private List<Header> notificationHeaders;
}
