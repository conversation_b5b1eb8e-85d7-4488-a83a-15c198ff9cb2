package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Accessors(chain = true)
public class PayoneerRequestCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    private AccountInfo accountInfo;
    private String number;
    private String email;
    private String deliveryEmail;
    private LocalDate birthday;
    private String gender;
    private Name name;
    private CustomerCompany company;
    private Addresses addresses;
    private Phones phones;
    private Registration registration;
}
