package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class RiskData implements Serializable {

    private static final long serialVersionUID = 1L;

    private RiskDataCustomer customer;
    private RiskDataShipping shipping;
    private RiskDataPayment payment;
    private Gift gift;
}
