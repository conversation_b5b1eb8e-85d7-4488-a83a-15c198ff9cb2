package com.zsmall.system.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.vo.transaction.TransactionOrderDetailVo;

/**
 * 交易记录数据处理-Service
 *
 * <AUTHOR> @date 2023年8月9日
 */
public interface PaymentHandleService {

    /**
     * 根据不同的实现判断交易记录详情数据处理使用哪个实现类
     *
     * @param transactionRecord
     * @return
     */
    boolean executeThisImpl(TransactionRecord transactionRecord);

    /**
     * 处理交易记录详情
     *
     * @param transactionRecord
     * @param pageQuery
     * @return
     */
    R<TableDataInfo<TransactionOrderDetailVo>> handleTransactionDetail(TransactionRecord transactionRecord, PageQuery pageQuery);

}
