package com.zsmall.system.biz.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.ijpay.payoneer.enums.PayoneerStatusEnum;
import com.ijpay.payoneer.exception.IJPayPayoneerException;
import com.ijpay.payoneer.model.in.InPaymentDebit;
import com.ijpay.payoneer.model.out.*;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.*;
import com.zsmall.common.exception.WalletException;
import com.zsmall.extend.payment.support.PayoneerSupport;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.system.biz.enums.PayoneerResultEnum;
import com.zsmall.system.biz.service.TenantPayoneerService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.entity.domain.TenantPayoneer;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.payment.PayoneerCommitBo;
import com.zsmall.system.entity.domain.bo.payment.PayoneerPaymentBo;
import com.zsmall.system.entity.domain.vo.extraSetting.PayoneerAccountBalanceVo;
import com.zsmall.system.entity.domain.vo.extraSetting.PayoneerAccountVo;
import com.zsmall.system.entity.domain.vo.extraSetting.PayoneerPaymentVo;
import com.zsmall.system.entity.domain.vo.payment.PayoneerCommitVo;
import com.zsmall.system.entity.iservice.ITenantPayoneerService;
import com.zsmall.system.entity.iservice.ITransactionReceiptService;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * tenant payoneer Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-06-17
 */
@RequiredArgsConstructor(onConstructor_ = { @Lazy })
@Service
@Slf4j
public class TenantPayoneerServiceImpl implements TenantPayoneerService {

    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final ITransactionRecordService iTransactionRecordService;
    private final ITransactionReceiptService iTransactionReceiptService;
    private final ITenantPayoneerService iTenantPayoneerService;
    private final PayoneerSupport payoneerSupport;
    private final TenantWalletService tenantWalletService;
    private final TenantPayoneerService tenantPayoneerService;

    /**
     * Payoneer充值/支付下单
     * @param bo
     * @return
     */
    @Override
    public R<PayoneerPaymentVo> getPaymentDebitInfo(PayoneerPaymentBo bo) {
        log.info("调用【getPaymentDebitInfo payoneer下单】接口 = {}", JSONUtil.toJsonStr(bo));

        Long id = bo.getId();
        BigDecimal transactionAmount = bo.getCost();
        String currency = StrUtil.isBlank(bo.getCurrency()) ? "USD" : bo.getCurrency();
        String description = StrUtil.isBlank(bo.getDescription()) ? "ZSMall充值" : bo.getDescription();

        TenantPayoneer tenantPayoneer = iTenantPayoneerService.getById(id);
        if (ObjectUtil.isEmpty(tenantPayoneer)) {
            return R.fail(ZSMallStatusCodeEnum.PAYONEER_EMPTY_ERROR);
        }

        String accountId = tenantPayoneer.getAccountId();
        log.info("accountId = {}", accountId);
        try {
            //payoneer余额
            PayoneerAccountBalanceVo payoneerBalance = this.getPayoneerBalance(accountId, currency);
            if (ObjectUtil.isEmpty(payoneerBalance)) {
                return R.fail(ZSMallStatusCodeEnum.PAYONEER_BALANCE_ERROR);
            }
//
            String transactionNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo);
            log.info("transactionNo = {}", transactionNo);
            PaymentDebitModel paymentDebitModel = paymentDebit(accountId, payoneerBalance.getBalanceId(), transactionAmount, currency, description, transactionNo);

            //获取payoneer手续费
            List<Fee> fees = paymentDebitModel.getFees();
            BigDecimal handlingFee = payoneerSupport.calculateFee(currency, fees);

            //存入金额
            BigDecimal cost = BigDecimal.ZERO;
            BigDecimal chargedAmount = paymentDebitModel.getAmounts().getCharged().getAmount();
            if (chargedAmount != null) {
                cost = chargedAmount;
            }

            PayoneerPaymentVo respBody = new PayoneerPaymentVo();
            respBody.setAmount(NumberUtil.round(transactionAmount, 2).toString());
            respBody.setPayoneerOrderNo(transactionNo);
            respBody.setCost(NumberUtil.round(cost.doubleValue(), 2).toString());
            respBody.setHandleFee(NumberUtil.round(handlingFee, 2).toString());
            respBody.setCommitId(paymentDebitModel.getCommitId());

            //添加payoneer充值记录
//            String transactionNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo);
            TransactionRecord transactions = new TransactionRecord();
            transactions.setTransactionNo(transactionNo);
            transactions.setTransactionType(TransactionTypeEnum.Recharge);
            transactions.setTransactionSubType(TransactionSubTypeEnum.Recharge);
            transactions.setTransactionAmount(cost);
            transactions.setTransactionState(TransactionStateEnum.Processing);
            transactions.setTransactionNote(description);
            transactions.setAfterBalance(new BigDecimal("0.00"));
            transactions.setBeforeBalance(new BigDecimal("0.00"));
            transactions.setCurrency(bo.getCurrency());
            transactions.setCurrencySymbol(bo.getCurrencySymbol());
            iTransactionRecordService.save(transactions);

            return R.ok(respBody);
        } catch (IJPayPayoneerException e) {
            log.error("IJPayPayoneerException - code = {}, error = {}, errorDescription = {}", e.getCode(), e.getError(),
                e.getErrorDescription(), e);
            if (StrUtil.equals("1503", e.getCode())) {
                return R.fail(ZSMallStatusCodeEnum.PAYONEER_INSUFFICIENT_BALANCE_ERROR);
            }
            return R.fail(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_COMMIT_ERROR);
        } catch (Exception e) {
            log.error("Exception = {}", e.getMessage(), e);
            return R.fail(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_COMMIT_ERROR);
        }
    }

    /**
     * Payoneer重定向
     *
     * @param request
     * @param response
     */
    @Override
    public void payoneerRedirect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        String type = request.getParameter("type");
        log.info("payoneerRedirect - code = {}, state = {}, type = {}", code, state, type);

        try {
            if (StrUtil.isNotBlank(code)) {
                RedissonClient client = RedisUtils.getClient();
                String lockKey = RedisConstants.ZSMALL_PAYONEER_CODE_LOCK + code;
                RLock lock = client.getLock(lockKey);
                lock.lock(60L, TimeUnit.SECONDS);
                toRedirectPath(code, state, response);
            } else if (StrUtil.isNotBlank(type)) {
                log.info("进入二次确认支付重定向");
                processResponseWithType(request, response, type);
            } else {
                responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_BINDING_FAILED);
            }
        } catch (IJPayPayoneerException e) {
            log.error("IJPayPayoneerException - code = {}, error = {}, errorDescription = {}",
                e.getCode(), e.getError(), e.getErrorDescription());
            //二次确认支付重定向
            if (StrUtil.equals("401", e.getCode())) {
                responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_APPLICATION_NOT_REGISTERED_ERROR);
            }
            if (StrUtil.equals(PayoneerStatusEnum.RequestExpired.getStatus(), e.getCode())) {
                // code = 2004, error = Request Expired, errorDescription = The request’s expiration time has passed
                responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_PAYMENT_TIMEOUT_ERROR);
            }

            responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_BINDING_FAILED);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_AUTHORIZATION_ERROR);
        }
    }

    /**
     * 获取注册地址
     *
     * @return
     */
    @Override
    public R<RegistrationLinkModel> getRegistrationLink() {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        // 检查是否完善信息
        ZSMallSystemEventUtils.checkDisInfoPerfection(loginUser.getTenantId());

        RegistrationLinkModel registrationLink = payoneerSupport.getRegistrationLink(TenantHelper.getTenantId());
        return R.ok(registrationLink);
    }

    /**
     * 取消支付下单
     * @param orderNo
     * @return
     * @throws RStatusCodeException
     */
    @Override
    public boolean cancelPaymentDebit(String orderNo) throws RStatusCodeException {
        TransactionRecord transactionRecord = iTransactionRecordService.findByTransactionNo(orderNo);
        if (transactionRecord == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.STORE_TRANSACTION_IS_EMPTY);
        }

        transactionRecord.setTransactionState(TransactionStateEnum.Canceled);
        transactionRecord.setTransactionNote(transactionRecord.getTransactionNote() + ",self-cancellation");

        return iTransactionRecordService.saveOrUpdate(transactionRecord);
    }

    /**
     * 支付确认提交
     * @param payoneerCommitBo
     * @return
     * @throws RStatusCodeException
     */
    @Override
    public PayoneerCommitVo toPaymentCommit(PayoneerCommitBo payoneerCommitBo) throws RStatusCodeException {
        String tenantPayoneerIdString = payoneerCommitBo.getTenantPayoneerId();
        String commitId = payoneerCommitBo.getCommitId();

        Long tenantPayoneerId = Long.parseLong(tenantPayoneerIdString);
        TenantPayoneer tenantPayoneer = iTenantPayoneerService.getById(tenantPayoneerId);
        if (tenantPayoneer == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYONEER_EMPTY_ERROR);
        }

        TransactionRecord transactionRecord = null;
        try {
            String accountId = tenantPayoneer.getAccountId();

            PaymentCommitModel<?> paymentCommitModel = payoneerSupport.paymentCommit(accountId, commitId);
            int status = paymentCommitModel.getStatus();
            PayoneerCommitVo commitVo = new PayoneerCommitVo();

            if (status == 200) {
                commitVo.setStatus(PayoneerStatusEnum.Completed.name());

                PaymentCommit paymentCommit = paymentCommitModel.getSuccessResult();
                log.info("paymentCommit = {}", JSONUtil.toJsonStr(paymentCommit));

                String orderNo = paymentCommit.getClientReferenceId();

                transactionRecord = iTransactionRecordService.findByTransactionNo(orderNo);
                if (transactionRecord == null) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TRANSACTIONS_QUERY_ERROR);
                }
                //支付失败，修改支付记录状态
                if (!StrUtil.equals(PayoneerStatusEnum.Completed.getStatus(), paymentCommit.getStatus().toString())) {
                    modifyTransactionFailStatus(transactionRecord);
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_FAIL);
                }

                tenantPayoneerService.reconfirmRechargeAndRecordTransactionCheck(transactionRecord, accountId, paymentCommit);

            } else if (status == 403) {
                PaymentCommit403 fail403Result = paymentCommitModel.getFail403Result();
                log.info("fail403Result = {}", JSONUtil.toJsonStr(fail403Result));
                String sessionId = fail403Result.getChallenge().getSessionId();
                Date expiresAt = fail403Result.getChallenge().getExpiresAt();
                long expires = DateUtil.between(new Date(), expiresAt, DateUnit.SECOND);
                log.info("expires = {}", expires);

                RedisUtils.setCacheObject(payoneerSupport.getRedisKey(sessionId), accountId, Duration.ofSeconds(expires));
                String url = fail403Result.getChallenge().getUrl();
                commitVo.setStatus(PayoneerResultEnum.Reconfirm.name());
                commitVo.setUrl(payoneerSupport.getReconfirmRedirect(url));
            } else {
                commitVo.setStatus(PayoneerResultEnum.Error.name());
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_COMMIT_ERROR);
            }
            return commitVo;
        } catch (IJPayPayoneerException e) {
            log.error("Payoneer Deposit = IJPayPayoneerException - code = {}, error = {}, errorDescription = {}", e.getCode(), e.getError(),
                e.getErrorDescription(), e);

            // 更新交易记录
            if (transactionRecord != null) {
                updateTransactionRecordWithIJPayPayoneerException(transactionRecord, e);
            }

            if (StrUtil.equals(PayoneerStatusEnum.RequestExpired.getStatus(), e.getCode())) {
                // code = 2004, error = Request Expired, errorDescription = The request’s expiration time has passed
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_TIMEOUT_ERROR);
            }
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_COMMIT_ERROR);
        } catch (WalletException e) {
            log.error("Payoneer Deposit = StoreWalletException=>{}", e.getMessage(), e);
            // 更新交易记录
            if (transactionRecord != null) {
                updateTransactionRecordWithWalletException(transactionRecord, e);
            }
            throw new RStatusCodeException(e.getStatusCode());
        } catch (RStatusCodeException e) {
            log.error("Payoneer Deposit = RStatusCodeException = {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            // 更新交易记录
            if (transactionRecord != null) {
                updateTransactionRecordWithException(transactionRecord, e);
            }
            log.error("Payoneer Deposit = Exception = {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYONEER_PAYMENT_COMMIT_ERROR);
        }
    }


    /**
     * 保存支付回执单
     * @param tenantId
     * @param accountId
     * @param paymentCommit
     * @param transactionRecord
     * @throws RStatusCodeException
     */
    private void saveTransactionReceipt(String tenantId, String accountId, PaymentCommit paymentCommit, TransactionRecord transactionRecord) throws RStatusCodeException {
        log.info("tenantId = {}, accountId = {}, paymentCommit = {}", tenantId, accountId, JSONUtil.toJsonStr(paymentCommit));

        //存入金额
        BigDecimal cost = BigDecimal.ZERO;
        BigDecimal chargedAmount = paymentCommit.getAmounts().getCharged().getAmount();
        String currency = paymentCommit.getAmounts().getCharged().getCurrency();
        if (chargedAmount != null) {
            cost = chargedAmount;
        }

        //获取payoneer手续费
        List<Fee> fees = paymentCommit.getFees();
        BigDecimal handlingFee = payoneerSupport.calculateFee(currency, fees);

        TransactionReceipt transactionReceipt = new TransactionReceipt();
        String transactionReceiptNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionReceiptNo);
        transactionReceipt.setTransactionReceiptNo(transactionReceiptNo);
        transactionReceipt.setTransactionsId(transactionRecord.getId());
        transactionReceipt.setTenantId(tenantId);
        transactionReceipt.setAccountId(accountId);
        transactionReceipt.setTransactionMethod(TransactionMethodEnum.OnlinePayoneer);
        transactionReceipt.setTransactionType(TransactionTypeEnum.Recharge);
        transactionReceipt.setTransactionTime(LocalDateTime.now());
        transactionReceipt.setReceiptTime(LocalDateTime.now());
        transactionReceipt.setTransactionAmount(cost);
        transactionReceipt.setTransactionFee(handlingFee);
        transactionReceipt.setNote("");
        transactionReceipt.setThirdChannelNo(paymentCommit.getPaymentId());
        transactionReceipt.setReviewState(ReceiptReviewStateEnum.Accepted);
        iTransactionReceiptService.save(transactionReceipt);
    }

    private void saveTransactionReceiptCheck(String tenantId, String accountId, PaymentCommit paymentCommit, TransactionRecord transactionRecord) throws RStatusCodeException {
        log.info("tenantId = {}, accountId = {}, paymentCommit = {}", tenantId, accountId, JSONUtil.toJsonStr(paymentCommit));

        //存入金额
        BigDecimal cost = BigDecimal.ZERO;
        BigDecimal chargedAmount = paymentCommit.getAmounts().getCharged().getAmount();
        String currency = paymentCommit.getAmounts().getCharged().getCurrency();
        if (chargedAmount != null) {
            cost = chargedAmount;
        }

        //获取payoneer手续费
        List<Fee> fees = paymentCommit.getFees();
        BigDecimal handlingFee = payoneerSupport.calculateFee(currency, fees);

        TransactionReceipt transactionReceipt = new TransactionReceipt();
        String transactionReceiptNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionReceiptNo);
        transactionReceipt.setTransactionReceiptNo(transactionReceiptNo);
        transactionReceipt.setTransactionsId(transactionRecord.getId());
        transactionReceipt.setTenantId(tenantId);
        transactionReceipt.setAccountId(accountId);
        transactionReceipt.setTransactionMethod(TransactionMethodEnum.OnlinePayoneer);
        transactionReceipt.setTransactionType(TransactionTypeEnum.Recharge);
        transactionReceipt.setTransactionTime(LocalDateTime.now());
        transactionReceipt.setReceiptTime(LocalDateTime.now());
        transactionReceipt.setTransactionAmount(cost);
        transactionReceipt.setTransactionFee(handlingFee);
        transactionReceipt.setNote("");
        transactionReceipt.setThirdChannelNo(paymentCommit.getPaymentId());
        transactionReceipt.setReviewState(ReceiptReviewStateEnum.Pending);
        transactionReceipt.setCurrency(transactionRecord.getCurrency());
        transactionReceipt.setCurrencySymbol(transactionRecord.getCurrencySymbol());
        iTransactionReceiptService.save(transactionReceipt);
    }

    /**
     * 处理携带Type类型回调
     *
     * @param request
     * @param response
     * @param type
     * @throws IOException
     */
    public void processResponseWithType(HttpServletRequest request, HttpServletResponse response, String type) throws Exception {
        String responsePath = request.getParameter("response_path");
        String sessionId = request.getParameter("session_id");
        log.info("processResponseWithType sessionId = {}, responsePath = {}", sessionId, responsePath);
        String redisKey = null;
        try {
            if (StrUtil.equals(type, "response") && StrUtil.isAllNotBlank(responsePath, sessionId)) {
                redisKey = payoneerSupport.getRedisKey(sessionId);
                String accountId = RedisUtils.getCacheObject(redisKey);
                if (StrUtil.isBlank(accountId)) {
                    //确认支付失效
                    responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_PAYMENT_TIMEOUT_ERROR);
                }

                PaymentCommit paymentCommit = payoneerSupport.getFromResponsePath(accountId, responsePath);
                log.info("paymentCommit = {}", paymentCommit);
                if(ObjectUtil.isNull(paymentCommit)){
                    responseSendRedirect(response, "error", ZSMallStatusCodeEnum.STORE_TRANSACTION_IS_EMPTY);
                }
                String orderNo = paymentCommit.getClientReferenceId();

                TransactionRecord transactionRecord = iTransactionRecordService.findByTransactionNoWithNotTenant(orderNo);
                if (transactionRecord == null) {
                    responseSendRedirect(response, "error", ZSMallStatusCodeEnum.STORE_TRANSACTION_IS_EMPTY);
                }
                //支付失败，修改支付记录状态
                if (!StrUtil.equals(PayoneerStatusEnum.Completed.getStatus(), paymentCommit.getStatus().toString())) {
                    modifyTransactionFailStatus(transactionRecord);
                    responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_PAYMENT_FAIL);
                }

                try {
                    tenantPayoneerService.reconfirmRechargeAndRecordTransactionCheck(transactionRecord, accountId, paymentCommit);
                } catch (WalletException e) {
                    log.error("processResponseWithType => payoneer reconfirm WalletException. {}", e.getMessage(), e);
                    // 更新交易记录
                    updateTransactionRecordWithWalletException(transactionRecord, e);
                } catch (Exception e) {
                    log.error("processResponseWithType => payoneer reconfirm Exception. {}", e.getMessage(), e);
                    // 更新交易记录
                    updateTransactionRecordWithException(transactionRecord, e);
                }

                this.responseSendRedirect(response, "ok", ZSMallStatusCodeEnum.REQUEST_SUCCESS, PayoneerResultEnum.Reconfirm.name());
            }
        } finally {
            if (StrUtil.isNotBlank(redisKey)) {
                RedisUtils.deleteObject(redisKey);
            }
        }
    }

    /**
     * 异常时，更新交易记录
     * @param transactionRecord
     * @param e
     */
    private void updateTransactionRecordWithIJPayPayoneerException(TransactionRecord transactionRecord, IJPayPayoneerException e) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);

        JSONObject message = JSONUtil.createObj().putOnce("message", e.getErrorDescription());
        transactionRecord.setFailureReason(message);
        iTransactionRecordService.updateById(transactionRecord);
    }

    /**
     * 异常时，更新交易记录
     * @param transactionRecord
     * @param e
     */
    private void updateTransactionRecordWithWalletException(TransactionRecord transactionRecord, WalletException e) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
        transactionRecord.setFailureReason(e.getLocaleMessage().toJSON());
        iTransactionRecordService.updateById(transactionRecord);
    }

    /**
     * 异常时，更新交易记录
     * @param transactionRecord
     * @param e
     */
    private void updateTransactionRecordWithException(TransactionRecord transactionRecord, Exception e) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
        JSONObject message = JSONUtil.createObj().putOnce("message", e.getMessage());
        transactionRecord.setFailureReason(message);
        iTransactionRecordService.updateById(transactionRecord);
    }


    /**
     * payoneer支付失败后修改交易记录信息状态为失败
     *
     * @param transactionRecord
     */
    private void modifyTransactionFailStatus(TransactionRecord transactionRecord) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
        iTransactionRecordService.save(transactionRecord);
    }

    /**
     * 二次确认支付成功后 处理钱包余额相关操作
     *
     * @param transactionRecord
     * @param accountId         账号Id
     * @param paymentCommit
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reconfirmRechargeAndRecordTransaction(TransactionRecord transactionRecord, String accountId, PaymentCommit paymentCommit) throws Exception {
        String tenantId = transactionRecord.getTenantId();
        // 保存交易回执单
        saveTransactionReceipt(tenantId, accountId, paymentCommit, transactionRecord);

        // 处理钱包余额相关操作
        tenantWalletService.walletChanges(transactionRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reconfirmRechargeAndRecordTransactionCheck(TransactionRecord transactionRecord, String accountId, PaymentCommit paymentCommit) throws Exception {
        String tenantId = transactionRecord.getTenantId();
        // 保存交易回执单
        saveTransactionReceiptCheck(tenantId, accountId, paymentCommit, transactionRecord);
    }



    /**
     * 响应重定向前端页面
     *
     * @param response
     * @param status
     * @param mallStatusCodeEnum
     * @throws IOException
     */
    private void responseSendRedirect(HttpServletResponse response, String status, ZSMallStatusCodeEnum mallStatusCodeEnum) throws IOException {
        this.responseSendRedirect(response, status, mallStatusCodeEnum, null);
    }


    /**
     * 重定向到响应页
     *
     * @param code
     * @param state
     * @param response
     */
    private void toRedirectPath(String code, String state, HttpServletResponse response) throws Exception {
        PayoneerAccountVo payoneerAccount = this.getUserAccount(code, state);
        log.info("payoneerAccount = {}", JSONUtil.toJsonStr(payoneerAccount));

        // 绑定payoneer用户
        if (payoneerAccount == null || StrUtil.isBlank(payoneerAccount.getTenantId())) {
            responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_AUTHORIZATION_EXPIRED);
        } else {
            //判断当前payoneer账户是否存在
            boolean itExist = isExistPayoneerAccount(payoneerAccount.getAccountId(), payoneerAccount.getTenantId());
            if (itExist) {
                responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_ACCOUNT_BEEN_BOUND_ERROR);
            } else {
                boolean saveResult = saveTenantPayoneer(payoneerAccount.getTenantId(), payoneerAccount.getAccountId());
                if (saveResult) {
                    responseSendRedirect(response, "ok", ZSMallStatusCodeEnum.REQUEST_SUCCESS);
                } else {
                    responseSendRedirect(response, "error", ZSMallStatusCodeEnum.PAYONEER_BINDING_FAILED);
                }
            }
        }
    }


    /**
     * 租户绑定payoneer操作
     *
     * @param tenantId
     * @param accountId
     * @return
     */
    private boolean saveTenantPayoneer(String tenantId, String accountId) {
        TenantPayoneer tenantPayoneer = new TenantPayoneer();
        tenantPayoneer.setTenantId(tenantId);
        tenantPayoneer.setAccountName(accountId);
        tenantPayoneer.setAccountId(accountId);
        boolean isExists = iTenantPayoneerService.isExistDefaultTenantPayoneer(tenantId);
        tenantPayoneer.setIsDefault(isExists ? 0 : 1);
        if (!isExists) {
            tenantPayoneer.setIsDefault(1);
        }
        return iTenantPayoneerService.save(tenantPayoneer);
    }

    /**
     * 判断payoneer账户是否存在
     *
     * @param accountId
     * @param tenantId
     * @return
     */
    private boolean isExistPayoneerAccount(String accountId, String tenantId) {
        return iTenantPayoneerService.isExistPayoneerAccount(accountId, tenantId);
    }


    /**
     * payoneer授权完成后解析accountId和userId
     *
     * @param code
     * @param state
     * @return
     */
    public PayoneerAccountVo getUserAccount(String code, String state) {
        log.info("getUserAccount code = {}, state = {}", code, state);
        PayoneerAccountVo dto;
        String payeeId = "";
        try {
            String accountId = payoneerSupport.getAccountId(code);
            List<String> strings = payoneerSupport.decryptState(state);
            log.info("getUserAccount strings = {}", strings);
            String redisKey = payoneerSupport.getRedisKey(strings.get(0));
            String tenantId = RedisUtils.getCacheObject(redisKey);

            dto = new PayoneerAccountVo();
            dto.setTenantId(StrUtil.isBlank(tenantId) ? null : tenantId);
            dto.setAccountId(accountId);
        } finally {
            if (StrUtil.isNotBlank(payeeId)) {
                // 清理授权绑定用户
                RedisUtils.deleteObject(payeeId);
            }
        }

        return dto;
    }




    /**
     * 根据payoneer账户id及币种获取payoneer账户余额
     *
     * @param accountId payoneer账户id
     * @param currency  币种类型
     * @return
     */
    public PayoneerAccountBalanceVo getPayoneerBalance(String accountId, String currency) {
        log.info("进入 getPayoneerBalance 方法");
        //获取payoneer账户余额列表
        BalanceList balances = payoneerSupport.getPayoneerBalanceList(accountId);
        log.info("balances = {}", JSONUtil.toJsonStr(balances));
        if(balances != null) {
            List<Account> accountList = balances.getBalances().getItems();
            PayoneerAccountBalanceVo balanceDto = new PayoneerAccountBalanceVo();
            accountList.forEach(l -> {
                if (StrUtil.equals(currency, l.getCurrency())) {
                    balanceDto.setBalanceId(l.getId());
                    balanceDto.setCurrency(l.getCurrency());
                    return;
                }
            });
            return balanceDto;
        }

        return null;
    }


    /**
     * 响应重定向前端页面
     *
     * @param response
     * @param status
     * @param mallStatusCodeEnum
     * @param reconfirm
     * @throws IOException
     */
    public void responseSendRedirect(HttpServletResponse response, String status, ZSMallStatusCodeEnum mallStatusCodeEnum, String reconfirm) throws IOException {
        UrlBuilder builder = UrlBuilder.of();
        builder.addQuery("status", status);
        builder.addQuery("error_code", mallStatusCodeEnum.getMessageCode());
        if (StrUtil.isNotBlank(reconfirm)) {
            builder.addQuery("reconfirm", reconfirm);
        }
        String url = payoneerSupport.getLastRedirectUrl(builder);
//        log.info("redirect url = {}", url);
        response.sendRedirect(url);
    }

    /**
     * payoneer下单
     *
     * @param accountId
     * @param balanceId
     * @param money
     * @param currency
     * @param description
     * @param orderNo
     * @return
     */
    private PaymentDebitModel paymentDebit(String accountId, String balanceId, BigDecimal money, String currency,
        String description, String orderNo) {

        description = StrUtil.isBlank(description) ? "ZSMall支付" : description;
        InPaymentDebit inPaymentDebit = new InPaymentDebit();
        inPaymentDebit.setAmount(money);
        inPaymentDebit.setBalanceId(balanceId);
        inPaymentDebit.setCurrency(currency);
        inPaymentDebit.setClientReferenceId(orderNo);
        inPaymentDebit.setDescription(description);
        //向账户付款
        return payoneerSupport.paymentDebit(accountId, inPaymentDebit);
    }

}
