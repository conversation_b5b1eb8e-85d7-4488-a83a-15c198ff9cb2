package com.zsmall.system.biz.service;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.zsmall.system.entity.domain.bo.payment.PaymentExtensionBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptReviewBo;
import com.zsmall.system.entity.domain.vo.payment.RechargeRecordVo;

/**
 * 交易扩展Service
 *
 * <AUTHOR> @create 2023年6月16日
 */
public interface PaymentExtensionService {

    /**
     * 获取汇款信息
     *
     * @param obj
     * @return
     */
    R<JSONObject> getRemittanceInfo(Object obj);

    /**
     * 提交交易回执单
     *
     * @param bo
     * @return
     */
    R<Void> submitPaymentReceipt(PaymentExtensionBo bo) throws Exception;

    /**
     * 获取充值详情
     * @param receiptNo
     * @return
     */
    R<RechargeRecordVo.RechargeRecordDetails> getRechargeRecordDetails(String receiptNo);

    /**
     * 审核支付回执单
     * @param bo
     * @return
     */
    boolean reviewPaymentReceipt(TransactionReceiptReviewBo bo) throws Exception;

    /**
     *导出充值记录
     * @param bo
     */
    void exportReceiptList(TransactionReceiptBo bo);

}
