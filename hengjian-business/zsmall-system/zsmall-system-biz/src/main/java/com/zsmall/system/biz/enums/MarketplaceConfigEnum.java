package com.zsmall.system.biz.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
public enum MarketplaceConfigEnum {
    NewRecommend( "新品推荐"),
    HotSelling( "爆款热卖"),
    WholesaleRecommend( "批发推荐"),
    SalesRanking( "销量排行榜"),
    GuessYouLike( "猜你喜欢"),
    SearchBox( "搜索框");

    private final String description;

    MarketplaceConfigEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    // 添加一个静态方法来根据字符串获取枚举实例
    public static MarketplaceConfigEnum fromString(String text) {
        if (text != null) {
            for (MarketplaceConfigEnum b : MarketplaceConfigEnum.values()) {
                if (text.equalsIgnoreCase(b.name())) {
                    return b;
                }
            }
        }
        return null; // 或者根据需要抛出异常
    }


}
