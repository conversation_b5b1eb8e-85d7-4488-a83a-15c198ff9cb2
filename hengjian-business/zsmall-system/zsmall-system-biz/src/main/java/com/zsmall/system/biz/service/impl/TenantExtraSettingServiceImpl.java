package com.zsmall.system.biz.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MessageUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.extend.verifycode.utils.VerifyCodeUtils;
import com.hengjian.system.domain.vo.SysUserVo;
import com.zsmall.common.constant.MallConstants;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.payment.WalletAutoPayEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.event.system.DistrPaymentInfoInitEvent;
import com.zsmall.system.biz.service.TenantExtraSettingService;
import com.zsmall.system.entity.domain.TenantExtraSetting;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.bo.extraSetting.PaymentPasswordBo;
import com.zsmall.system.entity.domain.bo.extraSetting.TenantAutoOnShelfBo;
import com.zsmall.system.entity.domain.bo.extraSetting.TenantPaymentVerifyBo;
import com.zsmall.system.entity.iservice.ITenantExtraSettingService;
import com.zsmall.system.entity.iservice.ITenantWalletService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TenantExtraSettingServiceImpl implements TenantExtraSettingService {

    private final ITenantExtraSettingService iTenantExtraSettingService;

    private final ITenantWalletService iTenantWalletService;

    /**
     * 事件 - 异步创建分销商支付信息
     *
     * @param paymentInfoInitEvent
     */
    @Async
    @EventListener
    public void initDistrPaymentInfoEventListener(DistrPaymentInfoInitEvent paymentInfoInitEvent) {
        log.info("initDistrPaymentInfoEventListener params = {}", JSONUtil.toJsonStr(paymentInfoInitEvent));
        String inTenantId = paymentInfoInitEvent.getInTenantId();

        List<TenantExtraSetting> tenantExtraSettingList = new ArrayList<>();
        // 设置默认关闭自动扣款
        TenantExtraSetting tenantExtraSetting = iTenantExtraSettingService.queryBySettingCodeAndTenantId(inTenantId,
            MallConstants.DistributorSettings.AutomaticallyDeduction);
        if (tenantExtraSetting == null) {
            tenantExtraSetting = new TenantExtraSetting();
            tenantExtraSetting.setSettingCode(MallConstants.DistributorSettings.AutomaticallyDeduction);
            tenantExtraSetting.setSettingValue(Boolean.FALSE.toString());
            tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
            tenantExtraSetting.setTenantId(inTenantId);
            tenantExtraSettingList.add(tenantExtraSetting);
        }


        // 设置默认无密码支付
        tenantExtraSetting = iTenantExtraSettingService.queryBySettingCodeAndTenantId(inTenantId,
            MallConstants.DistributorSettings.EnablePaymentPassword);
        if (tenantExtraSetting == null) {
            tenantExtraSetting = new TenantExtraSetting();
            tenantExtraSetting.setSettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
            tenantExtraSetting.setSettingValue(Boolean.FALSE.toString());
            tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
            tenantExtraSetting.setTenantId(inTenantId);
            tenantExtraSettingList.add(tenantExtraSetting);
        }

        if (CollUtil.isNotEmpty(tenantExtraSettingList)) {
            iTenantExtraSettingService.saveBatch(tenantExtraSettingList);
        }
    }


    /**
     * 发送不同方式的用于支付密码设置的验证码
     *
     * @param type
     * @return
     */
    @Override
    public boolean sendPaymentVerifyCode(String type) {
        SysUserVo tenantAdministrator = getSysUserVo();

        try {
            switch (type.toLowerCase()) {
                case "email":
                    String email = tenantAdministrator.getEmail();
                    String language = tenantAdministrator.getLanguage();

                    VerifyCodeUtils.paymentSendEmail(email, language);
                    break;
                case "sms":
                    String phonenumber = tenantAdministrator.getPhonenumber();
                    String areaCode = tenantAdministrator.getAreaCode();

                    VerifyCodeUtils.paymentSendPhoneNumber(phonenumber, areaCode);
                    break;
                default:
                    break;
            }

            return true;
        } catch (Exception e) {
            log.error("sendPaymentVerifyCode error. message = {}", e.getMessage(), e);
            ZSMallStatusCodeEnum statusCodeEnum = StrUtil.equals("sms", type.toLowerCase()) ?
                ZSMallStatusCodeEnum.USER_SEND_SMS_OTP_ERROR : ZSMallStatusCodeEnum.USER_EMAIL_SEND_ERROR;
            throw new RStatusCodeException(statusCodeEnum);
        }
    }

    /**
     * 校验短信验证码
     *
     * @param tenantPaymentVerifyBo
     * @return
     */
    @Override
    public boolean checkPaymentVerifyCode(TenantPaymentVerifyBo tenantPaymentVerifyBo) {
        String verifyCode = tenantPaymentVerifyBo.getVerifyCode();
        String type = tenantPaymentVerifyBo.getType();

        SysUserVo tenantAdministrator = getSysUserVo();

        String unique;
        if (StrUtil.equals("email", type.toLowerCase())) {
            unique = tenantAdministrator.getEmail();
        } else if (StrUtil.equals("sms", type.toLowerCase())) {
            unique = tenantAdministrator.getPhonenumber();
        } else {
            return false;
        }
        VerifyCodeUtils.paymentValidate(unique, verifyCode);
        return true;
    }

    /**
     * 更新设置支付密码
     *
     * @param paymentPasswordBo
     * @return
     */
    @Override
    public boolean updatePaymentPassword(PaymentPasswordBo paymentPasswordBo) {
        log.info("updatePaymentPassword params = {}", JSONUtil.toJsonStr(paymentPasswordBo));
        String newPassword = paymentPasswordBo.getNewPassword();
        String confirmPassword = paymentPasswordBo.getConfirmPassword();

        String newEncryptPwd = BCrypt.hashpw(newPassword);
        boolean checkPwd = BCrypt.checkpw(confirmPassword, newEncryptPwd);
        if (!checkPwd) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.COMFIRM_PASSWORD_IS_NOT_MATCH);
        }

        try {
            String tenantId = LoginHelper.getTenantId();
            TenantExtraSetting tenantExtraSetting = iTenantExtraSettingService.queryBySettingCodeAndTenantId(tenantId,
                MallConstants.DistributorSettings.PaymentPassword);
            if (tenantExtraSetting == null) {
                tenantExtraSetting = new TenantExtraSetting();
                tenantExtraSetting.setSettingCode(MallConstants.DistributorSettings.PaymentPassword);
                tenantExtraSetting.setSettingValue(newEncryptPwd);
                tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
                tenantExtraSetting.setTenantId(tenantId);
            } else {
                tenantExtraSetting.setSettingValue(newEncryptPwd);
                tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
            }

            iTenantExtraSettingService.saveOrUpdate(tenantExtraSetting);
        } catch (Exception e) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SETTING_PAYMENT_PASSWORD_ERROR);
        }
        return true;
    }

    /**
     * 更新自动上架配置
     *
     * @param bo
     * @return
     */
    @Override
    public boolean updateAutoOnShelf(TenantAutoOnShelfBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        Boolean enable = bo.getEnable();

        String tenantId = loginUser.getTenantId();
        TenantExtraSetting tenantExtraSetting = iTenantExtraSettingService.queryBySettingCodeAndTenantId(tenantId,
            MallConstants.SupplierSettings.AutomaticallyOnShelf);
        if (tenantExtraSetting == null) {
            tenantExtraSetting = new TenantExtraSetting();
            tenantExtraSetting.setSettingCode(MallConstants.SupplierSettings.AutomaticallyOnShelf);
            tenantExtraSetting.setSettingValue(enable.toString());
            tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
            tenantExtraSetting.setTenantId(tenantId);
        } else {
            tenantExtraSetting.setSettingValue(enable.toString());
            tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
        }
        iTenantExtraSettingService.saveOrUpdate(tenantExtraSetting);
        return true;
    }

    /**
     * 初始化额外设置
     *
     * @param tenantId
     * @param tenantType
     */
    @Override
    public void initExtraSetting(String tenantId, TenantType tenantType) {

        switch (tenantType) {
            case Distributor:
                break;
            case Supplier:
                TenantExtraSetting tenantExtraSetting = new TenantExtraSetting();
                tenantExtraSetting.setSettingCode(MallConstants.SupplierSettings.AutomaticallyOnShelf);
                tenantExtraSetting.setSettingValue("false");
                tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
                tenantExtraSetting.setTenantId(tenantId);
                iTenantExtraSettingService.save(tenantExtraSetting);

                break;
            case Manager:
                break;
            default:
                break;
        }

    }

    /**
     * 校验支付密码
     *
     * @param password
     */
    @Override
    public void checkPaymentPassword(String password) {
        TenantExtraSetting enablePaymentPasswordSetting = iTenantExtraSettingService.queryBySettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
        if (enablePaymentPasswordSetting != null) {
            Boolean EnablePaymentPassword = Boolean.valueOf(enablePaymentPasswordSetting.getSettingValue());

            if (EnablePaymentPassword && StrUtil.isBlank(password)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.INCORRECT_PAYMENT_PASSWORD);
            }

            if (EnablePaymentPassword) {
                TenantExtraSetting paymentPasswordSetting = iTenantExtraSettingService.queryBySettingCode(MallConstants.DistributorSettings.PaymentPassword );
                boolean checkPwd = false;
                if (paymentPasswordSetting != null) {
                    String settingValue = paymentPasswordSetting.getSettingValue();
                    checkPwd = BCrypt.checkpw(password, settingValue);
                }

                if (!checkPwd) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.INCORRECT_PAYMENT_PASSWORD);
                }
            }
        }
    }

    @Override
    public void checkPaymentPassword(String password, String tenantId) {

        TenantExtraSetting enablePaymentPasswordSetting = TenantHelper.ignore(()->iTenantExtraSettingService.queryBySettingCodeNoLogin(MallConstants.DistributorSettings.EnablePaymentPassword, tenantId));
        if (enablePaymentPasswordSetting != null) {
            Boolean EnablePaymentPassword = Boolean.valueOf(enablePaymentPasswordSetting.getSettingValue());

            if (EnablePaymentPassword && StrUtil.isBlank(password)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.INCORRECT_PAYMENT_PASSWORD);
            }

            if (EnablePaymentPassword) {
                TenantExtraSetting paymentPasswordSetting = iTenantExtraSettingService.queryBySettingCodeNoLogin(MallConstants.DistributorSettings.PaymentPassword, tenantId);
                boolean checkPwd = false;
                if (paymentPasswordSetting != null) {
                    String settingValue = paymentPasswordSetting.getSettingValue();
                    checkPwd = BCrypt.checkpw(password, settingValue);
                }

                if (!checkPwd) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.INCORRECT_PAYMENT_PASSWORD);
                }
            }
        }
    }

    @Override
    public Boolean checkAutoPayment(String tenantId) {
        Boolean autoPay = false;
        TenantExtraSetting enablePaymentPasswordSetting = iTenantExtraSettingService.queryBySettingCodeAndTenantId(tenantId, MallConstants.DistributorSettings.AutomaticallyDeduction);
        if (enablePaymentPasswordSetting != null) {
            try {
                autoPay = Boolean.valueOf(enablePaymentPasswordSetting.getSettingValue());
            } catch (Exception e) {
                log.error("检查是否开启自动支付出现未知错误，原因 {}", e.getMessage(), e);
            }
        }
        return autoPay;
    }

    @Override
    public Boolean checkWalletAutoPayment(String tenantId, String currency) {
        Boolean autoPay = false;
        TenantWallet tenantWallet = iTenantWalletService.queryByTenantIdAndCurrency(tenantId, currency);
        if (null != tenantWallet) {
            try {
                autoPay = tenantWallet.getIsAutoPay().equals(WalletAutoPayEnum.OPEN);
            } catch (Exception e) {
                log.error("检查钱包是否开启自动支付出现未知错，原因 {}", e.getMessage(), e);
            }
        }
        return autoPay;
    }

    /**
     * 检查自动上架设置
     *
     * @param tenantId
     * @return
     */
    @Override
    public Boolean checkAutoOnShelf(String tenantId) {
        Boolean autoOnShelf = false;
        TenantExtraSetting AutomaticallyOnShelf = iTenantExtraSettingService.queryBySettingCodeAndTenantId(tenantId, MallConstants.SupplierSettings.AutomaticallyOnShelf);
        if (AutomaticallyOnShelf != null) {
            try {
                autoOnShelf = Boolean.valueOf(AutomaticallyOnShelf.getSettingValue());
            } catch (Exception e) {
                log.error("自动上架设置值转换错误，将默认为false，原因 {}", e.getMessage(), e);
            }
        }
        return autoOnShelf;
    }

    @NotNull
    private static SysUserVo getSysUserVo() throws RStatusCodeException {
        SysUserVo tenantAdministrator = SystemEventUtils.getSysTenantAdministrator(LoginHelper.getTenantId());
        if (tenantAdministrator == null) {
            throw new RStatusCodeException(MessageUtils.message("tenant.administrator.not.found"));
        }
        return tenantAdministrator;
    }
}
