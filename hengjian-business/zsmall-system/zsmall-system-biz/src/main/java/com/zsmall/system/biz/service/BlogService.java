package com.zsmall.system.biz.service;


import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.BlogArticleEntity;
import com.zsmall.system.entity.domain.bo.blog.ReqBlogArticle;
import com.zsmall.system.entity.domain.bo.blog.ReqBlogArticleListBody;
import com.zsmall.system.entity.domain.bo.blog.ReqBlogCategoryBody;
import com.zsmall.system.entity.domain.dto.BlogQueryDTO;
import com.zsmall.system.entity.domain.vo.blog.BlogArticleBase;

/**
 * 博客相关-Service
 *
 * <AUTHOR>
 * @create 2022/1/8 17:11
 */
public interface BlogService extends IService<BlogArticleEntity> {

    /**
     * 保存博客文章
     *
     * @param apiRequest
     * @return
     */
    R<Void> saveBlogArticle(BlogArticleBase apiRequest);

    /**
     * 分页查询博客文章列表
     *
     * @param apiRequest
     * @return
     */
    R<TableDataInfo<BlogArticleBase>> queryBlogArticlePage(ReqBlogArticle apiRequest, PageQuery pageQuery);

    /**
     * 查询博客文章详情
     *
     * @param apiRequest
     * @return
     */
    R<BlogArticleBase> queryBlogArticleDetail(ReqBlogArticle apiRequest);

    /**
     * 删除博客文章
     *
     * @param apiRequest
     * @return
     */
    R<Void> deleteBlogArticle(ReqBlogArticleListBody apiRequest);

    /**
     * 查询博客分类
     *
     * @param apiRequest
     * @return
     */
    R<JSONArray> queryBlogCategory(ReqBlogCategoryBody apiRequest);

    /**
     * 翻页查询
     * @param page 翻页
     * @param query 查询条件
     * @return
     */
    IPage<BlogArticleEntity> getBlogPage(Page<BlogArticleEntity> page, BlogQueryDTO query);

//    R<JSONArray> testDelayIssueBlog();
}
