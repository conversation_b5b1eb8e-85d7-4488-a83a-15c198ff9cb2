package com.zsmall.system.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.system.biz.service.IBlogCategoryService;
import com.zsmall.system.entity.domain.BlogCategory;
import com.zsmall.system.entity.domain.bo.blog.BlogCategoryBo;
import com.zsmall.system.entity.domain.vo.blog.BlogCategoryVo;
import com.zsmall.system.entity.mapper.BlogCategoryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 博客分类Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BlogCategoryServiceImpl implements IBlogCategoryService {

    private final BlogCategoryMapper baseMapper;

    /**
     * 查询博客分类
     */
    @Override
    public BlogCategoryVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询博客分类列表
     */
    @Override
    public TableDataInfo<BlogCategoryVo> queryPageList(BlogCategoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BlogCategory> lqw = buildQueryWrapper(bo);
        Page<BlogCategoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询博客分类列表
     */
    @Override
    public List<BlogCategoryVo> queryList(BlogCategoryBo bo) {
        LambdaQueryWrapper<BlogCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BlogCategory> buildQueryWrapper(BlogCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BlogCategory> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCategoryCode()), BlogCategory::getCategoryCode, bo.getCategoryCode());
        lqw.eq(bo.getCategoryLevel() != null, BlogCategory::getCategoryLevel, bo.getCategoryLevel());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), BlogCategory::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getCategoryState() != null, BlogCategory::getCategoryState, bo.getCategoryState());
        return lqw;
    }

    /**
     * 新增博客分类
     */
    @Override
    public Boolean insertByBo(BlogCategoryBo bo) throws RStatusCodeException {
        BlogCategory add = MapstructUtils.convert(bo, BlogCategory.class);
        validEntityBeforeSave(add);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改博客分类
     */
    @Override
    public Boolean updateByBo(BlogCategoryBo bo) throws RStatusCodeException {
        BlogCategory update = MapstructUtils.convert(bo, BlogCategory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BlogCategory entity) throws RStatusCodeException {
        String categoryCode = entity.getCategoryCode();
        Long id = entity.getId();

        LambdaQueryWrapper<BlogCategory> codeExistLqw = Wrappers.lambdaQuery();
        codeExistLqw.eq(BlogCategory::getCategoryCode, StrUtil.trim(categoryCode));
        codeExistLqw.ne(ObjectUtil.isNotNull(id), BlogCategory::getId, id);
        boolean exists = baseMapper.exists(codeExistLqw);
        // 如果出现重复编码，则抛出异常
        if(exists) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.DUPLICATION_BLOG_CATEGORY_CODE);
        }

        String categoryOtherName = entity.getCategoryOtherName();
        if(StrUtil.isNotBlank(categoryOtherName) && !JSONUtil.isTypeJSON(categoryOtherName)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.BLOG_CATEGORY_OTHER_NAME_NOT_JSON);
        }
    }

    /**
     * 批量删除博客分类
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取可用的分类列表
     * @return
     */
    @Override
    public List<BlogCategoryVo> getEnableCategories() {
        LambdaQueryWrapper<BlogCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(BlogCategory::getCategoryState, 1);
        lqw.orderByAsc(BlogCategory::getCategorySort);

        return baseMapper.selectVoList(lqw, BlogCategoryVo.class);
    }
}
