package com.zsmall.system.biz.service.impl;

import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.system.biz.service.IPayonnerRefreshTokenRecordService;
import com.zsmall.system.entity.domain.PayonnerRefreshTokenRecord;
import com.zsmall.system.entity.domain.vo.PayonnerRefreshTokenRecordVo;
import com.zsmall.system.entity.mapper.PayonnerRefreshTokenRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * payonner刷新token记录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-10-10
 */
@RequiredArgsConstructor
@Service
public class PayonnerRefreshTokenRecordServiceImpl implements IPayonnerRefreshTokenRecordService {

    private final PayonnerRefreshTokenRecordMapper baseMapper;

    @Override
    public PayonnerRefreshTokenRecord getByAccountId(String accountId) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(PayonnerRefreshTokenRecord.class).eq(PayonnerRefreshTokenRecord::getAccountId, accountId).eq(PayonnerRefreshTokenRecord::getIsRefresh, 1));
    }

    @Override
    public void saveOrUpdate(PayonnerRefreshTokenRecord record) {
        if (null != record.getId()){
            baseMapper.updateById(record);
        }else {
            baseMapper.insert(record);
        }
    }


//    public TableDataInfo<PayonnerRefreshTokenRecordVo> queryPageList(PayonnerRefreshTokenRecord bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<PayonnerRefreshTokenRecord> lqw = buildQueryWrapper(bo);
//        Page<PayonnerRefreshTokenRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
//        return TableDataInfo.build(result);
//    }
//
//
//    private LambdaQueryWrapper<PayonnerRefreshTokenRecord> buildQueryWrapper(PayonnerRefreshTokenRecordBo bo) {
//        Map<String, Object> params = bo.getParams();
//        LambdaQueryWrapper<PayonnerRefreshTokenRecord> lqw = Wrappers.lambdaQuery();
//        lqw.eq(StringUtils.isNotBlank(bo.getAccountId()), PayonnerRefreshTokenRecord::getAccountId, bo.getAccountId());
//        lqw.eq(StringUtils.isNotBlank(bo.getTokenType()), PayonnerRefreshTokenRecord::getTokenType, bo.getTokenType());
//        lqw.eq(StringUtils.isNotBlank(bo.getAccessToken()), PayonnerRefreshTokenRecord::getAccessToken, bo.getAccessToken());
//        lqw.eq(StringUtils.isNotBlank(bo.getScope()), PayonnerRefreshTokenRecord::getScope, bo.getScope());
//        lqw.eq(bo.getConsentedOn() != null, PayonnerRefreshTokenRecord::getConsentedOn, bo.getConsentedOn());
//        lqw.eq(bo.getExpiresIn() != null, PayonnerRefreshTokenRecord::getExpiresIn, bo.getExpiresIn());
//        lqw.eq(StringUtils.isNotBlank(bo.getRefreshToken()), PayonnerRefreshTokenRecord::getRefreshToken, bo.getRefreshToken());
//        lqw.eq(bo.getRefreshTokenExpiresIn() != null, PayonnerRefreshTokenRecord::getRefreshTokenExpiresIn, bo.getRefreshTokenExpiresIn());
//        lqw.eq(bo.getIsRefresh() != null, PayonnerRefreshTokenRecord::getIsRefresh, bo.getIsRefresh());
//        return lqw;
//    }
//
}
