package com.zsmall.system.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.product.entity.domain.dto.productSku.*;
import com.zsmall.product.entity.iservice.IProductSkuStatisticsService;
import com.zsmall.system.biz.service.StatisticsProductSkuService;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.domain.bo.analysis.AnalysisProductSkuBo;
import com.zsmall.system.entity.domain.bo.analysis.AnalysisProductSkuInfoBo;
import com.zsmall.system.entity.domain.vo.analysis.RespAnalysisProductSkuBody;
import com.zsmall.system.entity.domain.vo.analysis.RespAnalysisProductSkuBulkBody;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/8 11:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsProductSkuServiceImpl implements StatisticsProductSkuService {

    private final IProductSkuStatisticsService iProductSkuStatisticsService;
    private final IDownloadRecordService iDownloadRecordService;
    private final FileProperties fileProperties;


    @InMethodLog(value = "商品统计列表")
    @Override
    public TableDataInfo getStatisticsList(AnalysisProductSkuBo bo, Page pageQuery) {
        String summaryType = bo.getSummaryType();
        TenantType tenantType = TenantType.valueOf(summaryType);
        switch (tenantType) {
            case Manager:
                return getProductSkuManageList(bo, pageQuery);
            case Supplier:
                return getProductSkuSupplierList(bo, pageQuery);
            case Distributor:
                return getProductSkuDistributorList(bo, pageQuery);
            default:
                break;
        }
        return null;
    }


    @InMethodLog(value = "导出sku统计数据文件")
    @Override
    public R<Void> exportSkuList(AnalysisProductSkuBo bo) {

        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            String fileName = StrUtil.format(FileNameConstants.PRODUCT_STATISTICS_EXPORT, DateUtil.format(new Date(), "yyMMdd-HH:mm:ss.SSSS"));

            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(DownloadTypePlusEnum.ProductActivityPrice);
            iDownloadRecordService.save(newRecord);

            ThreadUtil.execute(() -> {
                try {
                    TableDataInfo statisticsList = getStatisticsList(bo, new PageQuery().build());
                    List list = statisticsList.getRows();
                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFile = tempSavePath + File.separator + UUID.fastUUID().toString(true) + ".xlsx";
                    BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                    TenantType tenantType = TenantType.valueOf(bo.getSummaryType());
                     switch (tenantType) {
                        case Manager:
                            ExcelUtil.exportExcelWithLocale(list, "ProductStatistics", ProductSkuOrderManagerDTO.class, outputStream);
                            break;
                        case Supplier:
                            ExcelUtil.exportExcelWithLocale(list, "ProductStatistics", ProductSkuOrderSupplierDTO.class, outputStream);
                            break;
                        case Distributor:
                            ExcelUtil.exportExcelWithLocale(list, "ProductStatistics", ProductSkuOrderDistributorDTO.class, outputStream);
                            break;
                        default:
                            break;
                    }

                    outputStream.close();
                    BufferedInputStream inputStream = FileUtil.getInputStream(tempFile);

                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    // newRecord.setFileSize(StrUtil.toString(inputStream.length()));
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);
                    inputStream.close();
                    FileUtil.del(tempFile);
                } catch (Exception e) {
                    log.error("【导出商品统计】出现未知错误 {}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                }
            });
        }
        return R.ok();
    }

    @InMethodLog(value = "商品sku统计数据详情")
    @Override
    public R<RespAnalysisProductSkuBody> getAnalysisProductSkuInfo(AnalysisProductSkuInfoBo bo) {

        String itemNo = bo.getItemNo();
        List<String> searchDates = bo.getSearchDates();
        if (StrUtil.isBlank(itemNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        String startTime = "";
        String endTime = "";
        if (CollectionUtil.isEmpty(searchDates)) {
            DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
            startTime = DateUtil.format(DateUtil.offsetMonth(dateTime, -1), "yyyy-MM-dd");
            endTime = DateUtil.format(dateTime, "yyyy-MM-dd");
        } else {
            startTime = searchDates.get(0);
            endTime = searchDates.get(1);
        }

        String[] returnDates = new String[]{startTime, endTime};

        List<ProductSkuByDayDto> skuAnalysis = iProductSkuStatisticsService.getDayProductSkuByProductSkuCode(itemNo, startTime, endTime);
        List<AnalysisProductSkuByDayDto> ordersAnalysis = iProductSkuStatisticsService.getProductSkuOrdersByDay(itemNo, startTime, endTime);
        List<AnalysisProductSkuByDayDto> ordersChannelAnalysis = iProductSkuStatisticsService.getProductSkuOrdersByChannel(itemNo, startTime, endTime);
        List<AnalysisProductSkuByDayDto> ordersDropAnalysis = iProductSkuStatisticsService.getDayProductDropByProductSkuCode(itemNo, startTime, endTime);
        List<DistributorDataDto> disDataByProductSkuCode = iProductSkuStatisticsService.getDisListByProductSkuCode(itemNo, startTime, endTime);

        //将时间范围分解为每日集合
        List<String> dateTitle = getDateTitle(DateUtil.parseDate(startTime), DateUtil.parseDate(endTime));
        log.info("dateTitle => {}", JSONUtil.toJsonStr(dateTitle));

        List<BigDecimal> priceList = new ArrayList<>();
        List<BigDecimal> issuedOnBehalfPriceList = new ArrayList<>();
        List<Integer> inventoryNumList = new ArrayList<>();
        List<Integer> salesNumList = new ArrayList<>();
        List<Integer> issuedOnBehalfOrderNumList = new ArrayList<>();
        List<Integer> selfLiftingOrderNumList = new ArrayList<>();
        List<BigDecimal> issuedOnBehalfTotalPriceList = new ArrayList<>();
        List<BigDecimal> selfLiftingTotalPriceList = new ArrayList<>();
        List<Integer> dropNumList = new ArrayList<>();
        List<String> channelNameList = new ArrayList<>();
        List<Map<String, Object>> orderNumChannelList = new ArrayList<>();
        List<Map<String, Object>> orderPriceChannelList = new ArrayList<>();

        ProductSkuByDayDto sku = null;
        for (String d : dateTitle) {
            String currentDate = DateUtil.format(new Date(), "yyyy-MM-dd");
            if (StrUtil.equals(d, currentDate)) {
                continue;
            }
            List<ProductSkuByDayDto> skus = skuAnalysis.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            List<AnalysisProductSkuByDayDto> oas = ordersAnalysis.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            List<AnalysisProductSkuByDayDto> odas = ordersDropAnalysis.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skus)) {
                priceList.add(BigDecimal.ZERO);
                issuedOnBehalfPriceList.add(BigDecimal.ZERO);
                inventoryNumList.add(0);
            } else {
                ProductSkuByDayDto skuDto = skus.get(0);
                priceList.add(skuDto.getPickUpPrice());
                issuedOnBehalfPriceList.add(skuDto.getDropShippingPrice());
                inventoryNumList.add(skuDto.getInventoryNum());
                sku = skuDto;
            }

            if (CollectionUtil.isEmpty(oas)) {
                salesNumList.add(0);
                issuedOnBehalfOrderNumList.add(0);
                selfLiftingOrderNumList.add(0);
                issuedOnBehalfTotalPriceList.add(NumberUtil.toBigDecimal(0));
                selfLiftingTotalPriceList.add(NumberUtil.toBigDecimal(0));
            } else {
                AnalysisProductSkuByDayDto oasDto = oas.get(0);
                salesNumList.add(oasDto.getSalesNum());
                issuedOnBehalfOrderNumList.add(oasDto.getIssuedOnBehalfOrderNum());
                selfLiftingOrderNumList.add(oasDto.getSelfLiftingOrderNum());
                issuedOnBehalfTotalPriceList.add(NumberUtil.round(oasDto.getIssuedOnBehalfTotalPrice(), 2));
                selfLiftingTotalPriceList.add(NumberUtil.round(oasDto.getSelfLiftingTotalPrice(), 2));

            }

            if (CollectionUtil.isEmpty(odas)) {
                dropNumList.add(0);
            } else {
                dropNumList.add(odas.get(0).getBulkNum());
            }
        }

        ChannelTypeEnum[] values = ChannelTypeEnum.values();
        log.info("ChannelTypeEnums => {}", JSONUtil.toJsonStr(values));

        for (ChannelTypeEnum ct : values) {
            if (ObjectUtil.equal(ct, ChannelTypeEnum.OneLink)) {
                continue;
            }
            Map<String, Object> orderNumMap = new HashMap<>();
            Map<String, Object> orderPriceMap = new HashMap<>();
            orderNumMap.put("name", ct.name());
            orderPriceMap.put("name", ct.name());
            List<AnalysisProductSkuByDayDto> ocas = ordersChannelAnalysis.stream().filter(o -> StrUtil.equals(ct.name(), o.getChannelType())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(ocas)) {
                orderNumMap.put("value", 0);
                orderPriceMap.put("value", 0);
            } else {
                Integer orderNum = ocas.get(0).getOrderNum() == null ? 0 : ocas.get(0).getOrderNum();
                Integer salesNum = ocas.get(0).getSalesNum() == null ? 0 : ocas.get(0).getSalesNum();
                Double totalOriginPrice = ocas.get(0).getTotalOriginPrice();
                Double salesTotalPrice = ocas.get(0).getTotalDropShippingPrice() == null ? 0 : ocas.get(0).getTotalDropShippingPrice();
                orderNumMap.put("value", orderNum);
                orderPriceMap.put("value", totalOriginPrice);
            }
            orderNumChannelList.add(orderNumMap);
            orderPriceChannelList.add(orderPriceMap);
        }


        RespAnalysisProductSkuBody respBody = new RespAnalysisProductSkuBody();
        respBody.setDateList(dateTitle);
        respBody.setPickUpPriceList(priceList);
        respBody.setDropShippingPriceList(issuedOnBehalfPriceList);
        respBody.setInventoryNumList(inventoryNumList);
        respBody.setSalesNumList(salesNumList);
        respBody.setDropShippingOrderNumList(issuedOnBehalfOrderNumList);
        respBody.setPickUpOrderNumList(selfLiftingOrderNumList);
        respBody.setDropShippingTotalPriceList(issuedOnBehalfTotalPriceList);
        respBody.setPickUpTotalPriceList(selfLiftingTotalPriceList);
        respBody.setDropNumList(dropNumList);
        respBody.setChannelNameList(channelNameList);
        respBody.setOrderNumByChannel(orderNumChannelList);
        respBody.setOrderPriceByChannel(orderPriceChannelList);
        respBody.setDisDataList(disDataByProductSkuCode);
        respBody.setSearchDates(returnDates);
        return R.ok(respBody);
    }

    @InMethodLog(value = "商品sku统计数据分销商详情")
    @Override
    public R<RespAnalysisProductSkuBulkBody> getAnalysisProductSkuDistributorInfo(AnalysisProductSkuInfoBo bo) {
        String tenantId = bo.getTenantId();
        List<String> searchDates = bo.getSearchDates();
        if (StrUtil.isBlank(tenantId)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        String startTime = "";
        String endTime = "";
        if (CollectionUtil.isEmpty(searchDates)) {
            DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
            startTime = DateUtil.format(DateUtil.offsetMonth(dateTime, -1), "yyyy-MM-dd");
            endTime = DateUtil.format(dateTime, "yyyy-MM-dd");
        }else {
            startTime = searchDates.get(0);
            endTime = searchDates.get(1);
        }

        String[] returnDates = new String[] {startTime, endTime};

        List<AnalysisProductSkuByDayDto> psd = iProductSkuStatisticsService.getProductSkuOrderDisByDay(tenantId, startTime, endTime);
        List<AnalysisProductSkuByDayDto> psod = iProductSkuStatisticsService.getProductSkuOrderDistributionByDay(tenantId, startTime, endTime);
        List<AnalysisProductSkuByDayDto> psobc = iProductSkuStatisticsService.getProductSkuOrdersDisByChannel(tenantId, startTime, endTime);
        List<DistributorOrdersDataDto> skuList = iProductSkuStatisticsService.getSkuListByTenantId(tenantId, startTime, endTime);
        List<DistributorOrdersDataDto> supList = iProductSkuStatisticsService.getSupListByTenantId(tenantId, startTime, endTime);

        //将时间范围分解为每日集合
        List<String> dateTitle = getDateTitle(DateUtil.parseDate(startTime), DateUtil.parseDate(endTime));
        log.info("dateTitle => {}", JSONUtil.toJsonStr(dateTitle));

        List<Integer> skuDistributionNumList = new ArrayList<>();
        List<Integer> skuOrderNumList = new ArrayList<>();
        List<Integer> salesNumList = new ArrayList<>();
        List<Integer> issuedOnBehalfOrderNumList = new ArrayList<>();
        List<Integer> selfLiftingOrderNumList = new ArrayList<>();
        List<BigDecimal> issuedOnBehalfTotalPriceList = new ArrayList<>();
        List<BigDecimal> selfLiftingTotalPriceList = new ArrayList<>();

        List<String> channelNameList = new ArrayList<>();
        List<Map<String, Object>> orderNumChannelList = new ArrayList<>();
        List<Map<String, Object>> orderPriceChannelList = new ArrayList<>();

        dateTitle.stream().forEach(d ->{
            String currentDate = DateUtil.format(new Date(), "yyyy-MM-dd");
            if (StrUtil.equals(d, currentDate)) {
                return;
            }
            List<AnalysisProductSkuByDayDto> oas = psd.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            List<AnalysisProductSkuByDayDto> odas = psod.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(oas)) {
                skuOrderNumList.add(0);
                salesNumList.add(0);
                issuedOnBehalfOrderNumList.add(0);
                selfLiftingOrderNumList.add(0);
                issuedOnBehalfTotalPriceList.add(NumberUtil.toBigDecimal(0));
                selfLiftingTotalPriceList.add(NumberUtil.toBigDecimal(0));
            }else {
                AnalysisProductSkuByDayDto oasDto = oas.get(0);
                skuOrderNumList.add(oasDto.getSkuOrderNum());
                salesNumList.add(oasDto.getSalesNum());
                issuedOnBehalfOrderNumList.add(oasDto.getIssuedOnBehalfOrderNum());
                selfLiftingOrderNumList.add(oasDto.getSelfLiftingOrderNum());
                issuedOnBehalfTotalPriceList.add(NumberUtil.round(oasDto.getIssuedOnBehalfTotalPrice(), 2));
                selfLiftingTotalPriceList.add(NumberUtil.round(oasDto.getSelfLiftingTotalPrice(), 2));

            }

            if (CollectionUtil.isEmpty(odas)) {
                skuDistributionNumList.add(0);
            }else {
                skuDistributionNumList.add(odas.get(0).getSkuDistributionNum());
            }
        });

        ChannelTypeEnum[] values = ChannelTypeEnum.values();
        log.info("ChannelTypeEnums => {}", JSONUtil.toJsonStr(values));

        for (ChannelTypeEnum ct: values) {
            if (ObjectUtil.equal(ct, ChannelTypeEnum.OneLink)) {
                continue;
            }
            Map<String, Object> orderNumMap = new HashMap<>();
            Map<String, Object> orderPriceMap = new HashMap<>();
            orderNumMap.put("name", ct.name());
            orderPriceMap.put("name", ct.name());
            List<AnalysisProductSkuByDayDto> ocas = psobc.stream().filter(o -> StrUtil.equals(ct.name(), o.getChannelType())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(ocas)) {
                orderNumMap.put("value", 0);
                orderPriceMap.put("value", 0);
            }else {
                Integer orderNum = ocas.get(0).getOrderNum() == null ? 0 : ocas.get(0).getOrderNum();
                Integer salesNum = ocas.get(0).getSalesNum()  == null ? 0 : ocas.get(0).getSalesNum();
//                Double salesTotalPrice = ocas.get(0).getTotalDropShippingPrice()  == null ? 0 : ocas.get(0).getTotalDropShippingPrice();
                Double totalOriginPrice = ocas.get(0).getTotalOriginPrice();
                orderNumMap.put("value", orderNum);
                orderPriceMap.put("value", totalOriginPrice);
            }
            orderNumChannelList.add(orderNumMap);
            orderPriceChannelList.add(orderPriceMap);
        }


        RespAnalysisProductSkuBulkBody respBody = new RespAnalysisProductSkuBulkBody();
        respBody.setDateList(dateTitle);
        respBody.setSkuDistributionNumList(skuDistributionNumList);
        respBody.setSkuOrderNumList(skuOrderNumList);
        respBody.setSalesNumList(salesNumList);
        respBody.setDropShippingOrderNumList(issuedOnBehalfOrderNumList);
        respBody.setPickUpOrderNumList(selfLiftingOrderNumList);
        respBody.setDropShippingTotalPriceList(issuedOnBehalfTotalPriceList);
        respBody.setPickUpTotalPriceList(selfLiftingTotalPriceList);
        respBody.setChannelNameList(channelNameList);
        respBody.setOrderNumByChannel(orderNumChannelList);
        respBody.setOrderPriceByChannel(orderPriceChannelList);
        respBody.setSkuList(skuList);
        respBody.setSupList(supList);
        respBody.setSearchDates(returnDates);
        return R.ok(respBody);
    }

    @InMethodLog(value = "商品sku统计数据供应商详情")
    @Override
    public R<RespAnalysisProductSkuBody> getAnalysisProductSkuSupplierInfo(AnalysisProductSkuInfoBo bo) {

        String tenantId = bo.getTenantId();
        List<String> searchDates = bo.getSearchDates();
        if (StrUtil.isBlank(tenantId)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        String startTime = "";
        String endTime = "";
        if (CollectionUtil.isEmpty(searchDates)) {
            DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
            startTime = DateUtil.format(DateUtil.offsetMonth(dateTime, -1), "yyyy-MM-dd");
            endTime = DateUtil.format(dateTime, "yyyy-MM-dd");
        }else {
            startTime = searchDates.get(0);
            endTime = searchDates.get(1);
        }

        String[] returnDates = new String[] {startTime, endTime};

        List<ProductSkuByDayDto> skuAnalysis = iProductSkuStatisticsService.getDayProductSkuByTenantId(tenantId, startTime, endTime);
        List<AnalysisProductSkuByDayDto> ordersAnalysis = iProductSkuStatisticsService.getProductSkuOrderSupByDay(tenantId, startTime, endTime);
        List<AnalysisProductSkuByDayDto> ordersDropAnalysis = iProductSkuStatisticsService.getDayProductDropByTenantId(tenantId, startTime, endTime);
        List<AnalysisProductSkuByDayDto> ordersChannelAnalysis = iProductSkuStatisticsService.getProductSkuOrdersSupByChannel(tenantId, startTime, endTime);
        List<DistributorDataDto> disList = iProductSkuStatisticsService.getDisListByTenantId(tenantId, startTime, endTime);
        //将时间范围分解为每日集合
        List<String> dateTitle = getDateTitle(DateUtil.parseDate(startTime), DateUtil.parseDate(endTime));
        log.info("dateTitle => {}", JSONUtil.toJsonStr(dateTitle));
        List<Integer> inventoryNumList = new ArrayList<>();
        List<Integer> salesNumList = new ArrayList<>();
        List<Integer> issuedOnBehalfOrderNumList = new ArrayList<>();
        List<Integer> selfLiftingOrderNumList = new ArrayList<>();
        List<BigDecimal> issuedOnBehalfTotalPriceList = new ArrayList<>();
        List<BigDecimal> selfLiftingTotalPriceList = new ArrayList<>();
        List<Integer> dropNumList = new ArrayList<>();
        List<String> channelNameList = new ArrayList<>();
        List<Map<String, Object>> orderNumChannelList = new ArrayList<>();
        List<Map<String, Object>> orderPriceChannelList = new ArrayList<>();

        ProductSkuByDayDto sku = null;
        for (String d: dateTitle) {
            String currentDate = DateUtil.format(new Date(), "yyyy-MM-dd");
            if (StrUtil.equals(d, currentDate)) {
                continue;
            }

            List<ProductSkuByDayDto> skus = skuAnalysis.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            List<AnalysisProductSkuByDayDto> oas = ordersAnalysis.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            List<AnalysisProductSkuByDayDto> odas = ordersDropAnalysis.stream().filter(o -> StrUtil.equals(d, o.getDay())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skus)) {
                inventoryNumList.add(0);
            }else {
                ProductSkuByDayDto skuDto = skus.get(0);
                inventoryNumList.add(skuDto.getInventoryNum());
                sku = skuDto;
            }

            if (CollectionUtil.isEmpty(oas)) {
                salesNumList.add(0);
                issuedOnBehalfOrderNumList.add(0);
                selfLiftingOrderNumList.add(0);
                issuedOnBehalfTotalPriceList.add(NumberUtil.toBigDecimal(0));
                selfLiftingTotalPriceList.add(NumberUtil.toBigDecimal(0));
            }else {
                AnalysisProductSkuByDayDto oasDto = oas.get(0);
                salesNumList.add(oasDto.getSalesNum());
                issuedOnBehalfOrderNumList.add(oasDto.getIssuedOnBehalfOrderNum());
                selfLiftingOrderNumList.add(oasDto.getSelfLiftingOrderNum());
                issuedOnBehalfTotalPriceList.add(NumberUtil.round(oasDto.getIssuedOnBehalfTotalPrice(), 2));
                selfLiftingTotalPriceList.add(NumberUtil.round(oasDto.getSelfLiftingTotalPrice(), 2));
            }

            if (CollectionUtil.isEmpty(odas)) {
                dropNumList.add(0);
            }else {
                dropNumList.add(odas.get(0).getBulkNum());
            }
        }

        ChannelTypeEnum[] values = ChannelTypeEnum.values();
        log.info("ChannelTypeEnums => {}", JSONUtil.toJsonStr(values));

        for (ChannelTypeEnum ct: values) {
            if (ObjectUtil.equal(ct, ChannelTypeEnum.OneLink)) {
                continue;
            }
            Map<String, Object> orderNumMap = new HashMap<>();
            Map<String, Object> orderPriceMap = new HashMap<>();
            orderNumMap.put("name", ct.name());
            orderPriceMap.put("name", ct.name());
            List<AnalysisProductSkuByDayDto> ocas = ordersChannelAnalysis.stream().filter(o -> StrUtil.equals(ct.name(), o.getChannelType())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(ocas)) {
                orderNumMap.put("value", 0);
                orderPriceMap.put("value", 0);
            }else {
                Integer orderNum = ocas.get(0).getOrderNum() == null ? 0 : ocas.get(0).getOrderNum();
                Integer salesNum = ocas.get(0).getSalesNum() == null ? 0 : ocas.get(0).getSalesNum();
                Double totalOriginPrice = ocas.get(0).getTotalOriginPrice();
//                Double salesTotalPrice = ocas.get(0).getTotalDropShippingPrice() == null ? 0 : ocas.get(0).getTotalDropShippingPrice();
                orderNumMap.put("value", orderNum);
                orderPriceMap.put("value", totalOriginPrice);
            }
            orderNumChannelList.add(orderNumMap);
            orderPriceChannelList.add(orderPriceMap);
        }


        RespAnalysisProductSkuBody respBody = new RespAnalysisProductSkuBody();
        respBody.setDateList(dateTitle);
        respBody.setInventoryNumList(inventoryNumList);
        respBody.setSalesNumList(salesNumList);
        respBody.setDropShippingOrderNumList(issuedOnBehalfOrderNumList);
        respBody.setPickUpOrderNumList(selfLiftingOrderNumList);
        respBody.setDropShippingTotalPriceList(issuedOnBehalfTotalPriceList);
        respBody.setPickUpTotalPriceList(selfLiftingTotalPriceList);
        respBody.setDropNumList(dropNumList);
        respBody.setChannelNameList(channelNameList);
        respBody.setOrderNumByChannel(orderNumChannelList);
        respBody.setOrderPriceByChannel(orderPriceChannelList);
        respBody.setDisDataList(disList);
        respBody.setSearchDates(returnDates);
        return R.ok(respBody);
    }


    @InMethodLog(value = "获取统计productSku列表")
    private TableDataInfo<ProductSkuOrderManagerDTO> getProductSkuManageList(AnalysisProductSkuBo bo, Page pageQuery) {

        IPage<ProductSkuOrderManagerDTO> resultPage = this.getSkuResultPage(bo, pageQuery);
        List<ProductSkuOrderManagerDTO> records = resultPage.getRecords();
        return TableDataInfo.build(records, resultPage.getTotal());
    }


    @InMethodLog(value = "获取分销商统计列表列表")
    private TableDataInfo<ProductSkuOrderSupplierDTO> getProductSkuSupplierList(AnalysisProductSkuBo bo, Page pageQuery) {
        IPage<ProductSkuOrderSupplierDTO> resultPage = this.getSupplierResultPage(bo, pageQuery);

        List<ProductSkuOrderSupplierDTO> records = resultPage.getRecords();
        return TableDataInfo.build(records, resultPage.getTotal());

    }


    @InMethodLog(value = "获取供应商统计列表")
    private TableDataInfo<ProductSkuOrderDistributorDTO> getProductSkuDistributorList(AnalysisProductSkuBo bo, Page pageQuery) {
        IPage<ProductSkuOrderDistributorDTO> resultPage = this.getDistributorResultPage(bo, pageQuery);

        List<ProductSkuOrderDistributorDTO> records = resultPage.getRecords();
        return TableDataInfo.build(records, resultPage.getTotal());
    }

    @InMethodLog(value = "分页获取sku统计数据")
    private IPage<ProductSkuOrderManagerDTO> getSkuResultPage(AnalysisProductSkuBo bo, Page pageQuery) {
        ProductSkuDTO dto = new ProductSkuDTO();
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();
        String sortType = bo.getSortType();

        if (StrUtil.isNotBlank(queryValue)) {
            dto.setQueryType(queryType);
            dto.setQueryValue(queryValue);
        }

        Integer pageNo = (int) pageQuery.getCurrent();
        Integer pageSize = (int) pageQuery.getSize();

        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        //查询
        OrderItem orderItem;
        if (StrUtil.equals("PriceAsc", sortType)) {//价格
            orderItem = OrderItem.asc("pick_up_price");
        } else if (StrUtil.equals("PriceDesc", sortType)) {
            orderItem = OrderItem.desc("pick_up_price");
        } else if (StrUtil.equals("StockAsc", sortType)) {//库存
            orderItem = OrderItem.asc("stock_total");
        } else if (StrUtil.equals("StockDesc", sortType)) {
            orderItem = OrderItem.desc("stock_total");
        } else if (StrUtil.equals("CreatedTimeDesc", sortType)) {//上架时间
            orderItem = OrderItem.desc("create_time");
        } else if (StrUtil.equals("CreatedTimeAsc", sortType)) {
            orderItem = OrderItem.asc("create_time");
        } else if (StrUtil.equals("SaleAsc", sortType)) {//销售件总数
            orderItem = OrderItem.asc("sales_num");
        } else if (StrUtil.equals("OrdersAsc", sortType)) {//订单总数
            orderItem = OrderItem.asc("order_total_num");
        } else if (StrUtil.equals("OrdersDesc", sortType)) {
            orderItem = OrderItem.desc("order_total_num");
        } else if (StrUtil.equals("AfterSaleOrdersAsc", sortType)) {//售后订单总数
            orderItem = OrderItem.asc("restock_total_num");
        } else if (StrUtil.equals("AfterSaleOrdersDesc", sortType)) {
            orderItem = OrderItem.desc("restock_total_num");
        } else if (StrUtil.equals("DropNumAsc", sortType)) {//被收藏数（分销商数）
            orderItem = OrderItem.asc("drop_num_by_dis");
        } else if (StrUtil.equals("DropNumDesc", sortType)) {
            orderItem = OrderItem.desc("drop_num_by_dis");
        } else if (StrUtil.equals("DealEffectivenessAsc", sortType)) {//订单处理时效
            orderItem = OrderItem.asc("order_deal_effectiveness");
        } else if (StrUtil.equals("DealEffectivenessDesc", sortType)) {
            orderItem = OrderItem.desc("order_deal_effectiveness");
        } else if (StrUtil.equals("DownloadedAsc", sortType)) {//被下载此处（分销商）
            orderItem = OrderItem.asc("downloaded_by_dis");
        } else if (StrUtil.equals("DownloadedDesc", sortType)) {
            orderItem = OrderItem.desc("downloaded_by_dis");
        } else {//默认平台销量的top 100 sku展示
            orderItem = OrderItem.desc("sales_num");
        }

        Page<ProductSkuOrderManagerDTO> queryPage = new Page<>(pageNo, pageSize);
        queryPage.addOrder(orderItem);

        log.info("getProductSkuList dtoParam = {}", JSONUtil.toJsonStr(dto));
        IPage<ProductSkuOrderManagerDTO> resultPage = iProductSkuStatisticsService.getProductSkuPageByDto(queryPage, dto);
        return resultPage;
    }


    @InMethodLog(value = "分页获取供应商统计数据")
    private IPage<ProductSkuOrderSupplierDTO> getSupplierResultPage(AnalysisProductSkuBo bo, Page pageQuery) {

        ProductSkuDTO dto = new ProductSkuDTO();
        String queryValue = bo.getQueryValue();
        String sortType = bo.getSortType();
        List<String> searchDates = bo.getSearchDates();
        if (CollectionUtil.isNotEmpty(searchDates)) {
            dto.setStartTime(searchDates.get(0));
            dto.setEndTime(searchDates.get(1));
        }
        if (StrUtil.isNotBlank(queryValue)) {
            dto.setQueryValue(queryValue);
        }

        log.info("getProductSkuSupList dtoParam = {}", JSONUtil.toJsonStr(dto));

        Integer pageNo = (int) pageQuery.getCurrent();
        Integer pageSize = (int) pageQuery.getSize();

        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        //查询
        OrderItem orderItem;
        if (StrUtil.equals("SkuNumAsc", sortType)) {//SKU总数
            orderItem = OrderItem.asc("sku_num");
        } else if (StrUtil.equals("SkuNumDesc", sortType)) {
            orderItem = OrderItem.desc("sku_num");
        } else if (StrUtil.equals("OrderTotalPriceDesc", sortType)) {//出单总金额
            orderItem = OrderItem.desc("order_total_price");
        } else if (StrUtil.equals("OrderTotalPriceAsc", sortType)) {
            orderItem = OrderItem.asc("order_total_price");
        } else if (StrUtil.equals("SaleAsc", sortType)) {//销售件总数
            orderItem = OrderItem.asc("sales_num");
        } else if (StrUtil.equals("OrdersAsc", sortType)) {//订单总数
            orderItem = OrderItem.asc("order_total_num");
        } else if (StrUtil.equals("OrdersDesc", sortType)) {
            orderItem = OrderItem.desc("order_total_num");
        } else if (StrUtil.equals("AfterSaleOrdersAsc", sortType)) {//售后订单总数
            orderItem = OrderItem.asc("restock_total_num");
        } else if (StrUtil.equals("AfterSaleOrdersDesc", sortType)) {
            orderItem = OrderItem.desc("restock_total_num");
        } else if (StrUtil.equals("DropNumAsc", sortType)) {//被收藏数（分销商数）
            orderItem = OrderItem.asc("drop_num_by_dis");
        } else if (StrUtil.equals("DropNumDesc", sortType)) {
            orderItem = OrderItem.desc("drop_num_by_dis");
        } else if (StrUtil.equals("DealEffectivenessAsc", sortType)) {//订单处理时效
            orderItem = OrderItem.asc("order_deal_effectiveness");
        } else if (StrUtil.equals("DealEffectivenessDesc", sortType)) {
            orderItem = OrderItem.desc("order_deal_effectiveness");
        } else {//默认平台销量的top 100 sku展示
            orderItem = OrderItem.desc("sales_num");
        }
        Page<ProductSkuOrderSupplierDTO> queryPage = new Page<>(pageNo, pageSize);
        queryPage.addOrder(orderItem);

        log.info("getProductSkuList dtoParam = {}", JSONUtil.toJsonStr(dto));
        IPage<ProductSkuOrderSupplierDTO> resultPage = iProductSkuStatisticsService.getProductSkuSupplierPageByDto(queryPage, dto);
        return resultPage;
    }


    @InMethodLog(value = "分页获取分销商统计数据")
    private IPage<ProductSkuOrderDistributorDTO> getDistributorResultPage(AnalysisProductSkuBo bo, Page pageQuery) {

        ProductSkuDTO dto = new ProductSkuDTO();
        String queryValue = bo.getQueryValue();
        String sortType = bo.getSortType();
        List<String> searchDates = bo.getSearchDates();
        if (CollectionUtil.isNotEmpty(searchDates)) {
            dto.setStartTime(searchDates.get(0));
            dto.setEndTime(searchDates.get(1));
        }

        if (StrUtil.isNotBlank(queryValue)) {
            dto.setQueryValue(queryValue);
        }

        log.info("getProductSkuBulkList dtoParam = {}", JSONUtil.toJsonStr(dto));

        Integer pageNo = (int) pageQuery.getCurrent();
        Integer pageSize = (int) pageQuery.getSize();

        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        //查询
        OrderItem orderItem;
        if (StrUtil.equals("SkuOrderNumAsc", sortType)) {//出单SKU总数
            orderItem = OrderItem.asc("sku_order_num");
        } else if (StrUtil.equals("SkuOrderNumDesc", sortType)) {
            orderItem = OrderItem.desc("sku_order_num");
        } else if (StrUtil.equals("SkuDistributionNumAsc", sortType)) {//铺货的SKU总数
            orderItem = OrderItem.asc("sku_distribution_num");
        } else if (StrUtil.equals("SkuDistributionNumDesc", sortType)) {
            orderItem = OrderItem.desc("sku_distribution_num");
        } else if (StrUtil.equals("OrderTotalPriceDesc", sortType)) {//出单总金额
            orderItem = OrderItem.desc("order_total_price");
        } else if (StrUtil.equals("OrderTotalPriceAsc", sortType)) {
            orderItem = OrderItem.asc("order_total_price");
        } else if (StrUtil.equals("SaleAsc", sortType)) {//销售件总数
            orderItem = OrderItem.asc("sales_num");
        } else if (StrUtil.equals("OrdersAsc", sortType)) {//订单总数
            orderItem = OrderItem.asc("order_total_num");
        } else if (StrUtil.equals("OrdersDesc", sortType)) {
            orderItem = OrderItem.desc("order_total_num");
        } else if (StrUtil.equals("AfterSaleOrdersAsc", sortType)) {//售后订单总数
            orderItem = OrderItem.asc("restock_total_num");
        } else if (StrUtil.equals("AfterSaleOrdersDesc", sortType)) {
            orderItem = OrderItem.desc("restock_total_num");
        } else if (StrUtil.equals("DropNumAsc", sortType)) {//已收藏产品的SKU
            orderItem = OrderItem.asc("drop_num_by_dis");
        } else if (StrUtil.equals("DropNumDesc", sortType)) {
            orderItem = OrderItem.desc("drop_num_by_dis");
        } else if (StrUtil.equals("ClickedSkuAsc", sortType)) {//已点击的产品SKU
            orderItem = OrderItem.asc("clicked_sku");
        } else if (StrUtil.equals("ClickedSkuDesc", sortType)) {
            orderItem = OrderItem.desc("clicked_sku");
        } else if (StrUtil.equals("DownloadedAsc", sortType)) {//已下载产品的SKU
            orderItem = OrderItem.asc("downloaded_by_dis");
        } else if (StrUtil.equals("DownloadedDesc", sortType)) {
            orderItem = OrderItem.desc("downloaded_by_dis");
        } else {//默认平台销量的top 100 sku展示
            orderItem = OrderItem.desc("sales_num");
        }
        Page<ProductSkuOrderDistributorDTO> queryPage = new Page<>(pageNo, pageSize);
        queryPage.addOrder(orderItem);

        log.info("getProductSkuList dtoParam = {}", JSONUtil.toJsonStr(dto));
        IPage<ProductSkuOrderDistributorDTO> resultPage = iProductSkuStatisticsService.getProductSkuDistributorPageByDto(queryPage, dto);
        return resultPage;
    }

    @InMethodLog(value = "通过时间范围获取 每日时间集合")
    private List<String> getDateTitle(Date startTime, Date endTime) {

        long dayNum = DateUtil.between(startTime, endTime, DateUnit.DAY);
        List<String> dayList = new ArrayList<>();
        dayList.add(DateUtil.format(startTime, "yyyy-MM-dd"));
        for (int i = 1; i <= dayNum; i++) {
            DateTime dateTime = DateUtil.offsetDay(startTime, i);
            dayList.add(DateUtil.format(dateTime, "yyyy-MM-dd"));
        }

        return dayList;
    }


}
