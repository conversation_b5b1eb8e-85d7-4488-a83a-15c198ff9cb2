package com.zsmall.system.biz.service;


import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.bo.billDatails.BillDetailsBo;
import com.zsmall.system.entity.domain.vo.billDetails.BillDetailsVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 账单明细-newService接口
 *
 * <AUTHOR> <PERSON>
 * @date 2024-08-26
 */
public interface IBillDetailsService {

    /**
     * 查询账单明细-new
     */
    BillDetailsVo queryById(Long id);

    /**
     * 查询账单明细-new列表
     */
    TableDataInfo<BillDetailsVo> queryPageList(BillDetailsBo bo, PageQuery pageQuery);

    /**
     * 查询账单明细-new列表
     */
    List<BillDetailsVo> queryList(BillDetailsBo bo);

    /**
     * 新增账单明细-new
     */
    Boolean insertByBo(BillDetailsBo bo);

    /**
     * 修改账单明细-new
     */
    Boolean updateByBo(BillDetailsBo bo);

    /**
     * 校验并批量删除账单明细-new信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);




    void dealBillDate(String billNo);

    /**
     *
     * @param tenantIds
     */
    /**
     * @description: 生成分销商账单信息
     * @author: Len
     * @date: 2024/9/9 11:29
     * @param: tenantId 分销商租户ID
     * @param: startTimeStr 开始时间月/日
     * @param: endTimeStr 结束时间月/日
     * @param: startTime 开始时间
     * @param: endTime  结束时间
     **/
  //  void generatedDistributorBillByTenant(Object tenantIds,String startTimeStr, String endTimeStr,String startTime,String endTime);


    /**
     * @description: 生成供应商账单信息
     * @author: Len
     * @date: 2024/9/9 11:21
     * @param: tenantId 供应商租户ID
     * @param: startTimeStr 开始时间月/日
     * @param: endTimeStr 结束时间月/日
     * @param: startTime 开始时间
     * @param: endTime  结束时间
     **/
  //  void generatedSupplierBillByTenant(Object tenantId, String startTimeStr, String endTimeStr, String startTime, String endTime);
}
